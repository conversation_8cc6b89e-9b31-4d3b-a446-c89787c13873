import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, Home, WifiOff } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorType: 'component' | 'api' | 'network' | 'unknown';
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError?: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorType: 'unknown'
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Determine error type based on error properties
    let errorType: 'component' | 'api' | 'network' | 'unknown' = 'component';

    if (error.message.includes('fetch') || error.message.includes('network')) {
      errorType = 'network';
    } else if (error.message.includes('404') || error.message.includes('500') || error.message.includes('API')) {
      errorType = 'api';
    }

    return {
      hasError: true,
      error,
      errorType
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    console.error('Error caught by boundary:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });

    // Enhanced error logging with more context
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorType: this.state.errorType,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      referrer: document.referrer,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      localStorage: this.getLocalStorageInfo(),
      sessionStorage: this.getSessionStorageInfo(),
    };

    console.error('Detailed error information:', errorDetails);

    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorDetails);
    }
  }

  private getLocalStorageInfo = (): Record<string, string | null> => {
    try {
      const info: Record<string, string | null> = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          // Only log non-sensitive keys
          if (!key.includes('token') && !key.includes('password') && !key.includes('secret')) {
            info[key] = localStorage.getItem(key);
          }
        }
      }
      return info;
    } catch {
      return { error: 'Unable to access localStorage' };
    }
  };

  private getSessionStorageInfo = (): Record<string, string | null> => {
    try {
      const info: Record<string, string | null> = {};
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key) {
          // Only log non-sensitive keys
          if (!key.includes('token') && !key.includes('password') && !key.includes('secret')) {
            info[key] = sessionStorage.getItem(key);
          }
        }
      }
      return info;
    } catch {
      return { error: 'Unable to access sessionStorage' };
    }
  };

  private logErrorToService = (error: Error, errorDetails: Record<string, unknown>): void => {
    // Here you would integrate with an error tracking service like Sentry, LogRocket, etc.
    // Example:
    // Sentry.captureException(error, { extra: errorDetails });

    // For now, we'll just log to console in a structured way
    console.group('🚨 Production Error Report');
    console.error('Error:', error);
    console.table(errorDetails);
    console.groupEnd();
  };

  handleResetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorType: 'unknown'
    });
  };

  getErrorIcon = () => {
    switch (this.state.errorType) {
      case 'network':
        return <WifiOff className="w-8 h-8 text-red-600" />;
      case 'api':
        return <AlertTriangle className="w-8 h-8 text-orange-600" />;
      default:
        return <AlertTriangle className="w-8 h-8 text-red-600" />;
    }
  };

  getErrorMessage = () => {
    const { errorType } = this.state;

    switch (errorType) {
      case 'network':
        return {
          title: 'Network Connection Error',
          message: 'Unable to connect to the server. Please check your internet connection and try again.'
        };
      case 'api':
        return {
          title: 'Server Error',
          message: 'The server encountered an error while processing your request. Please try again in a moment.'
        };
      default:
        return {
          title: 'Something went wrong',
          message: 'An unexpected error occurred in the application. Our team has been notified.'
        };
    }
  };

  render(): React.ReactNode {
    if (this.state.hasError) {
      // Custom fallback component if provided
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error || undefined} resetError={this.handleResetError} />;
      }

      // Default error UI with enhanced error handling
      const errorMessage = this.getErrorMessage();

      return (
        <div className="min-h-screen bg-slate-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              {this.getErrorIcon()}
            </div>

            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              {errorMessage.title}
            </h1>

            <p className="text-gray-600 mb-6">
              {errorMessage.message}
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 p-4 bg-red-50 rounded-lg text-left">
                <summary className="font-medium text-red-800 cursor-pointer mb-2">
                  Error Details (Development Mode)
                </summary>
                <pre className="text-xs text-red-700 overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={this.handleResetError}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>
              
              <button
                onClick={() => window.location.href = '/'}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center"
              >
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorFallback?: React.ComponentType<{ error?: Error; resetError?: () => void }>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={errorFallback}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};