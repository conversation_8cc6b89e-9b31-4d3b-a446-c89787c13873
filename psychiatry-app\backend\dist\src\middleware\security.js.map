{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../../src/middleware/security.ts"], "names": [], "mappings": ";;;;;;AACA,4EAA2C;AAC3C,oDAA4B;AAC5B,2CAAgD;AAChD,2CAAwC;AAS3B,QAAA,gBAAgB,GAAG,IAAA,4BAAS,EAAC;IACxC,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,EAAE,EAAE,CAAC;IACpE,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,KAAK,EAAE,EAAE,CAAC;IAC/D,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,wDAAwD;KAChE;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;QACvC,MAAM,IAAI,uBAAc,CAAC,wDAAwD,CAAC,CAAC;IACrF,CAAC;CACF,CAAC,CAAC;AAKU,QAAA,aAAa,GAAG,IAAA,4BAAS,EAAC;IACrC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,0DAA0D;KAClE;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,sBAAsB,EAAE,IAAI;IAC5B,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;QACvC,MAAM,IAAI,uBAAc,CAAC,0DAA0D,CAAC,CAAC;IACvF,CAAC;CACF,CAAC,CAAC;AAKU,QAAA,sBAAsB,GAAG,IAAA,4BAAS,EAAC;IAC9C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,0DAA0D;KAClE;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;QACvC,MAAM,IAAI,uBAAc,CAAC,0DAA0D,CAAC,CAAC;IACvF,CAAC;CACF,CAAC,CAAC;AAKU,QAAA,eAAe,GAAG,IAAA,gBAAM,EAAC;IACpC,qBAAqB,EAAE;QACrB,UAAU,EAAE;YACV,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACvC,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;YACrC,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACrB;KACF;IACD,yBAAyB,EAAE,KAAK;IAChC,IAAI,EAAE;QACJ,MAAM,EAAE,QAAQ;QAChB,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,IAAI;KACd;CACF,CAAC,CAAC;AAKI,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAErF,MAAM,QAAQ,GAAG,CAAC,GAAQ,EAAO,EAAE;QACjC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAE5B,OAAO,GAAG;iBACP,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC;iBAClE,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;iBAC5B,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;iBAC1B,IAAI,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,SAAS,GAAQ,EAAE,CAAC;YAC1B,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACtB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5B,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IAEF,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;QACd,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA1CW,QAAA,aAAa,iBA0CxB;AAKW,QAAA,WAAW,GAAG;IACzB,MAAM,EAAE,CAAC,MAA0B,EAAE,QAAsD,EAAE,EAAE;QAE7F,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,cAAc,GAAG;YACrB,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;YACnD,uBAAuB;YACvB,uBAAuB;YACvB,uBAAuB;YACvB,uBAAuB;YACvB,uBAAuB;SACxB,CAAC;QAGF,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEzC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7D,cAAc,EAAE;QACd,QAAQ;QACR,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,eAAe;QACf,QAAQ;KACT;IACD,cAAc,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC;CAClD,CAAC;AAKK,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACrF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAGzB,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAC9B,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC;QACpC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC;KACzC,CAAC,CAAC;IAEH,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;QACpC,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,MAAM,EAAE,GAAG,CAAC,UAAU;YACtB,QAAQ;YACR,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAGF,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YAC1B,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YACjC,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AApCW,QAAA,aAAa,iBAoCxB"}