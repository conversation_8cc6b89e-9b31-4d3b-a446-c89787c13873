<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Authentication Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        textarea { width: 100%; height: 200px; margin: 10px 0; }
        .token-display { word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>JWT Authentication Test</h1>
        
        <div class="section">
            <h2>1. Login Test</h2>
            <button onclick="testLogin()">Test Login (admin/admin123!)</button>
            <div id="loginResult"></div>
        </div>

        <div class="section">
            <h2>2. Protected Route Test</h2>
            <button onclick="testProtectedRoute()">Test Analytics Dashboard</button>
            <div id="protectedResult"></div>
        </div>

        <div class="section">
            <h2>3. Debug Information</h2>
            <textarea id="debugLog" readonly></textarea>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        let accessToken = '';
        
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toISOString();
            debugLog.value += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').value = '';
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = 'Testing login...';
            
            try {
                log('🔍 Starting login test...');
                
                const response = await fetch('http://localhost:3002/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123!'
                    })
                });

                log(`📡 Login response status: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`📦 Login response data: ${JSON.stringify(data, null, 2)}`);

                if (response.ok && data.success) {
                    accessToken = data.data.accessToken;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Successful!</h3>
                            <p><strong>User:</strong> ${data.data.user.username} (${data.data.user.role})</p>
                            <p><strong>Token Preview:</strong></p>
                            <div class="token-display">${accessToken.substring(0, 100)}...</div>
                        </div>
                    `;
                    log(`✅ Login successful! Token received: ${accessToken.substring(0, 50)}...`);
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                        </div>
                    `;
                    log(`❌ Login failed: ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                const errorMsg = `Network error: ${error.message}`;
                resultDiv.innerHTML = `<div class="error"><h3>❌ Network Error</h3><p>${errorMsg}</p></div>`;
                log(`❌ ${errorMsg}`);
            }
        }

        async function testProtectedRoute() {
            const resultDiv = document.getElementById('protectedResult');
            
            if (!accessToken) {
                resultDiv.innerHTML = '<div class="error">❌ Please login first to get an access token</div>';
                log('❌ No access token available. Please login first.');
                return;
            }

            resultDiv.innerHTML = 'Testing protected route...';
            
            try {
                log('🔍 Starting protected route test...');
                log(`🔑 Using token: ${accessToken.substring(0, 50)}...`);
                
                const response = await fetch('http://localhost:3002/api/analytics/dashboard', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                log(`📡 Protected route response status: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`📦 Protected route response data: ${JSON.stringify(data, null, 2)}`);

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Protected Route Access Successful!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Data Keys:</strong> ${Object.keys(data).join(', ')}</p>
                        </div>
                    `;
                    log(`✅ Protected route access successful!`);
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Protected Route Access Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                        </div>
                    `;
                    log(`❌ Protected route access failed: ${response.status} - ${data.error || 'Unknown error'}`);
                }
            } catch (error) {
                const errorMsg = `Network error: ${error.message}`;
                resultDiv.innerHTML = `<div class="error"><h3>❌ Network Error</h3><p>${errorMsg}</p></div>`;
                log(`❌ ${errorMsg}`);
            }
        }

        // Initialize
        log('🚀 JWT Authentication Test Page Loaded');
        log('📋 Instructions: 1) Click "Test Login" first, 2) Then click "Test Analytics Dashboard"');
    </script>
</body>
</html>
