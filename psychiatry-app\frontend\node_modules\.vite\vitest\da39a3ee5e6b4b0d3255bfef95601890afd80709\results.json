{"version": "3.2.4", "results": [[":src/features/history-taking/__tests__/disorders-registry.test.ts", {"duration": 37.61120000000028, "failed": true}], [":src/features/patients/__tests__/usePatients.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/integration/patient-workflow.test.tsx", {"duration": 0, "failed": true}], [":src/features/auth/__tests__/useAuth.test.tsx", {"duration": 0, "failed": true}], [":src/components/ui/__tests__/Button.test.tsx", {"duration": 0, "failed": true}], [":src/test/App.test.tsx", {"duration": 1722.9866000000002, "failed": true}]]}