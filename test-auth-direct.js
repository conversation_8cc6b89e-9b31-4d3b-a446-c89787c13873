// Direct authentication test without CORS issues
const http = require('http');

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (error) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testAuthentication() {
  console.log('🔍 Testing JWT Authentication Flow (Direct HTTP)...\n');

  try {
    // Step 1: Test login
    console.log('1. Testing login...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const loginData = JSON.stringify({
      username: 'admin',
      password: 'admin123!'
    });

    const loginResponse = await makeRequest(loginOptions, loginData);
    
    console.log('📡 Login response status:', loginResponse.status);
    console.log('📦 Login response data:', JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.status === 200 && loginResponse.data.success) {
      console.log('✅ Login successful!');
      const accessToken = loginResponse.data.data.accessToken;
      console.log('🔑 Access token received:', accessToken.substring(0, 50) + '...\n');

      // Step 2: Test protected route
      console.log('2. Testing protected route...');
      const protectedOptions = {
        hostname: 'localhost',
        port: 3002,
        path: '/api/analytics/dashboard',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        }
      };

      const protectedResponse = await makeRequest(protectedOptions);
      
      console.log('📡 Protected route response status:', protectedResponse.status);
      console.log('📦 Protected route response data:', JSON.stringify(protectedResponse.data, null, 2));

      if (protectedResponse.status === 200) {
        console.log('✅ Protected route access successful!');
        console.log('🎉 Authentication flow working correctly!');
      } else {
        console.log('❌ Protected route access failed');
        console.log('🔍 This is the JWT authentication error we need to debug!');
      }
    } else {
      console.log('❌ Login failed');
      console.log('Error details:', loginResponse.data);
    }
  } catch (error) {
    console.error('❌ Network error:', error.message);
  }
}

// Run the test
testAuthentication();
