const axios = require('axios');

const API_BASE = 'http://localhost:3002/api';

async function testAuthentication() {
  console.log('🔍 Testing JWT Authentication Flow...\n');

  try {
    // Step 1: Test login
    console.log('1. Testing login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      username: 'admin',
      password: 'admin123!'
    });

    console.log('✅ Login successful!');
    console.log('Response status:', loginResponse.status);
    console.log('Access token received:', loginResponse.data.data.accessToken ? 'Yes' : 'No');
    
    const accessToken = loginResponse.data.data.accessToken;
    console.log('Token preview:', accessToken.substring(0, 50) + '...\n');

    // Step 2: Test protected route
    console.log('2. Testing protected route access...');
    const protectedResponse = await axios.get(`${API_BASE}/analytics/dashboard`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Protected route access successful!');
    console.log('Response status:', protectedResponse.status);
    console.log('Response data keys:', Object.keys(protectedResponse.data));

  } catch (error) {
    console.error('❌ Authentication test failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Status text:', error.response.statusText);
      console.error('Error data:', error.response.data);
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
    
    // Additional debugging info
    console.error('\n🔍 Debug info:');
    console.error('Error config:', error.config);
  }
}

// Test JWT token decoding
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    
    console.log('\n🔍 JWT Token Analysis:');
    console.log('Header:', header);
    console.log('Payload:', payload);
    console.log('Expires at:', new Date(payload.exp * 1000));
    console.log('Issued at:', new Date(payload.iat * 1000));
    
    return { header, payload };
  } catch (error) {
    console.error('Failed to decode JWT:', error.message);
    return null;
  }
}

testAuthentication();
