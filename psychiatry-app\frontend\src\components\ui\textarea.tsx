import React from 'react';
import { cn } from '../../lib/utils';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  variant?: 'default' | 'outline' | 'ghost' | 'filled';
  textareaSize?: 'default' | 'sm' | 'lg';
}

// Define textarea variants for use in other components
export const textareaVariants = {
  base: 'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  variant: {
    default: '',
    outline: 'border border-input',
    ghost: 'border-none bg-transparent',
    filled: 'border-none bg-secondary/50'
  },
  size: {
    default: 'min-h-[80px] px-3 py-2',
    sm: 'min-h-[60px] px-2 py-1 text-xs',
    lg: 'min-h-[120px] px-4 py-3 text-lg'
  }
};

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          textareaVariants.base,
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Textarea.displayName = 'Textarea';

export { Textarea };
