import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select } from './ui/select';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import { usePatientStore } from '../store/patientStore';
import { LAB_TEMPLATES, getLabTemplate, type LabTemplate, type LabParameter } from '../data/labTemplates';
import { TestTube, Plus, Calendar, AlertCircle, CheckCircle, XCircle, Save, Loader2 } from 'lucide-react';
import { useToast } from '../hooks/use-toast';
import { format } from 'date-fns';
import axios from 'axios';

// API Configuration
const API_BASE_URL = 'http://localhost:3002';
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

interface LabResultValue {
  value: string | number;
  unit?: string;
  isAbnormal?: boolean;
}

interface LabFlag {
  flag: string;
  severity: string;
  note: string;
}

interface LabResult {
  id: string;
  patientId: string;
  testType: string;
  testDate: string;
  orderedBy: string;
  labName?: string;
  results: Record<string, LabResultValue>;
  normalRanges?: Record<string, { min?: number; max?: number; unit?: string }>;
  flags?: Record<string, 'high' | 'low' | 'critical' | 'normal' | LabFlag>;
  notes?: string;
  status: string;
  createdAt: string;
  patient?: {
    id: string;
    patientId: string;
    firstName: string;
    lastName: string;
  };
}

export const LabDataManager = () => {
  const { selectedPatient } = usePatientStore();
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [labResults, setLabResults] = useState<LabResult[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<LabTemplate | null>(null);
  const [formData, setFormData] = useState({
    testType: '',
    orderedBy: '',
    labName: '',
    testDate: new Date().toISOString().split('T')[0],
    notes: '',
    results: {} as Record<string, LabResultValue>,
    normalRanges: {} as Record<string, { min?: number; max?: number; unit?: string }>,
    flags: {} as Record<string, 'high' | 'low' | 'critical' | 'normal' | LabFlag>
  });

  const fetchLabResults = useCallback(async () => {
    if (!selectedPatient) return;

    setLoading(true);
    try {
      const response = await api.get(`/api/lab-results?patientId=${selectedPatient.id}`);
      setLabResults(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch lab results:', error);
      toast({
        title: "Error",
        description: "Failed to fetch lab results",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [selectedPatient, toast]);

  useEffect(() => {
    if (selectedPatient) {
      fetchLabResults();
    }
  }, [selectedPatient, fetchLabResults]);



  const handleTestTypeChange = (testType: string) => {
    const template = getLabTemplate(testType);
    setSelectedTemplate(template || null);
    setFormData({
      ...formData,
      testType,
      results: {},
      normalRanges: {},
      flags: {}
    });
  };

  const handleParameterChange = (parameter: LabParameter, value: string) => {
    const numericValue = parameter.type === 'number' ? parseFloat(value) || 0 : value;
    const labResultValue: LabResultValue = {
      value: numericValue,
      unit: parameter.unit,
      isAbnormal: false
    };

    setFormData(prev => ({
      ...prev,
      results: {
        ...prev.results,
        [parameter.key]: labResultValue
      },
      normalRanges: {
        ...prev.normalRanges,
        [parameter.key]: parameter.normalRange
      }
    }));

    // Auto-flag abnormal values for numeric parameters
    if (parameter.type === 'number' && parameter.normalRange.min !== undefined && parameter.normalRange.max !== undefined) {
      const num = parseFloat(value);
      if (!isNaN(num)) {
        const isAbnormal = num < parameter.normalRange.min! || num > parameter.normalRange.max!;
        if (isAbnormal) {
          setFormData(prev => ({
            ...prev,
            flags: {
              ...prev.flags,
              [parameter.key]: {
                flag: num < parameter.normalRange.min! ? 'LOW' : 'HIGH',
                severity: 'MILD',
                note: `Value outside normal range: ${parameter.normalRange.reference}`
              }
            }
          }));
        } else {
          // Remove flag if value becomes normal
          setFormData(prev => {
            const newFlags = { ...prev.flags };
            delete newFlags[parameter.key];
            return { ...prev, flags: newFlags };
          });
        }
      }
    }
  };

  const handleSubmitLabResult = async () => {
    if (!selectedPatient) {
      toast({
        title: "No patient selected",
        description: "Please select a patient first.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.testType || !formData.orderedBy || !formData.testDate) {
      toast({
        title: "Required fields missing",
        description: "Please fill in test type, ordered by, and test date.",
        variant: "destructive",
      });
      return;
    }

    // Validate required parameters
    if (selectedTemplate) {
      const missingRequired = selectedTemplate.parameters
        .filter(param => param.required && !formData.results[param.key])
        .map(param => param.name);

      if (missingRequired.length > 0) {
        toast({
          title: "Required parameters missing",
          description: `Please fill in: ${missingRequired.join(', ')}`,
          variant: "destructive",
        });
        return;
      }
    }

    setLoading(true);
    try {
      const labResultData = {
        patientId: selectedPatient.id,
        testType: formData.testType,
        testDate: new Date(formData.testDate).toISOString(),
        orderedBy: formData.orderedBy,
        labName: formData.labName || undefined,
        results: formData.results,
        normalRanges: Object.keys(formData.normalRanges).length > 0 ? formData.normalRanges : undefined,
        flags: Object.keys(formData.flags).length > 0 ? formData.flags : undefined,
        notes: formData.notes || undefined,
        status: 'COMPLETED'
      };

      await api.post('/api/lab-results', labResultData);

      toast({
        title: "Lab result created",
        description: `${selectedTemplate?.displayName || formData.testType} result has been added successfully.`,
      });

      // Reset form
      setFormData({
        testType: '',
        orderedBy: '',
        labName: '',
        testDate: new Date().toISOString().split('T')[0],
        notes: '',
        results: {},
        normalRanges: {},
        flags: {}
      });
      setSelectedTemplate(null);

      // Refresh lab results
      await fetchLabResults();

    } catch (error: any) {
      console.error('Failed to create lab result:', error);
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to create lab result. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'in_progress':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const hasAbnormalFlags = (flags: Record<string, any> | undefined) => {
    return flags ? Object.keys(flags).length > 0 : false;
  };

  if (!selectedPatient) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <TestTube className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600">Please select a patient to manage lab data.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Add New Lab Result */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Plus className="h-5 w-5 text-blue-600" />
            <span>Create Lab Result</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Test Type Selection */}
          <div>
            <Label htmlFor="testType">Test Type</Label>
            <Select data-testid="test-type-select" value={formData.testType} onChange={(e) => handleTestTypeChange(e.target.value)}>
              <option value="">Select test type</option>
              {LAB_TEMPLATES.map((template) => (
                <option key={template.testType} value={template.testType}>
                  {template.displayName}
                </option>
              ))}
            </Select>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="orderedBy">Ordered By *</Label>
              <Input
                id="orderedBy"
                data-testid="ordered-by-input"
                value={formData.orderedBy}
                onChange={(e) => setFormData({ ...formData, orderedBy: e.target.value })}
                placeholder="Dr. Smith"
                required
              />
            </div>
            <div>
              <Label htmlFor="testDate">Test Date *</Label>
              <Input
                id="testDate"
                data-testid="test-date-input"
                type="date"
                value={formData.testDate}
                onChange={(e) => setFormData({ ...formData, testDate: e.target.value })}
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="labName">Lab Name</Label>
            <Input
              id="labName"
              value={formData.labName}
              onChange={(e) => setFormData({ ...formData, labName: e.target.value })}
              placeholder="Central Laboratory"
            />
          </div>

          {/* Template-based Parameters */}
          {selectedTemplate && (
            <div className="space-y-4">
              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-2">
                  {selectedTemplate.displayName} Parameters
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  {selectedTemplate.description}
                </p>

                <div className="grid grid-cols-1 gap-4">
                  {selectedTemplate.parameters.map((parameter) => (
                    <div key={parameter.key} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <Label htmlFor={parameter.key} className="font-medium">
                          {parameter.name} {parameter.required && <span className="text-red-500">*</span>}
                        </Label>
                        <span className="text-xs text-gray-500">
                          {parameter.unit}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          id={parameter.key}
                          type={parameter.type === 'number' ? 'number' : 'text'}
                          value={typeof formData.results[parameter.key] === 'object'
                            ? formData.results[parameter.key]?.value?.toString() || ''
                            : formData.results[parameter.key]?.toString() || ''}
                          onChange={(e) => handleParameterChange(parameter, e.target.value)}
                          placeholder={parameter.type === 'number' ? '0.0' : 'Enter value'}
                          step={parameter.type === 'number' ? '0.01' : undefined}
                          required={parameter.required}
                        />
                        <div className="text-xs text-gray-600 flex items-center">
                          Normal: {parameter.normalRange.reference}
                        </div>
                      </div>

                      {formData.flags[parameter.key] && (
                        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                          <span className="font-medium text-yellow-800">
                            {typeof formData.flags[parameter.key] === 'object'
                              ? (formData.flags[parameter.key] as LabFlag)?.flag || 'FLAG'
                              : String(formData.flags[parameter.key]) || 'FLAG'}:
                          </span>
                          <span className="text-yellow-700 ml-1">
                            {typeof formData.flags[parameter.key] === 'object'
                              ? (formData.flags[parameter.key] as LabFlag)?.note || 'Abnormal value detected'
                              : 'Abnormal value detected'}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              placeholder="Additional notes or observations..."
            />
          </div>

          <Button
            onClick={handleSubmitLabResult}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            disabled={loading || !formData.testType}
          >
            {loading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {loading ? 'Creating...' : 'Create Lab Result'}
          </Button>
        </CardContent>
      </Card>

      {/* Existing Lab Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TestTube className="h-5 w-5 text-blue-600" />
            <span>Lab Results ({labResults.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 text-blue-600 mx-auto mb-3 animate-spin" />
              <p className="text-slate-600">Loading lab results...</p>
            </div>
          ) : labResults.length === 0 ? (
            <div className="text-center py-8">
              <TestTube className="h-8 w-8 text-slate-400 mx-auto mb-3" />
              <p className="text-slate-600">No lab results recorded yet.</p>
              <p className="text-sm text-slate-500">Create lab results using the form on the left.</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {(labResults || []).map((lab) => {
                const template = getLabTemplate(lab.testType);
                const hasFlags = lab.flags && hasAbnormalFlags(lab.flags);

                return (
                  <div key={lab.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-slate-900">
                          {template?.displayName || lab.testType}
                        </h4>
                        <p className="text-sm text-slate-600">
                          Ordered by: {lab.orderedBy}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(lab.status)}
                        <Badge className={getStatusColor(lab.status)}>
                          {lab.status}
                        </Badge>
                        {hasFlags && (
                          <Badge className="bg-yellow-100 text-yellow-800">
                            Flagged
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Results Display */}
                    <div className="space-y-2">
                      <h5 className="text-sm font-medium text-slate-700">Results:</h5>
                      <div className="grid grid-cols-1 gap-2">
                        {Object.entries(lab.results).map(([key, value]) => {
                          const parameter = template?.parameters.find(p => p.key === key);
                          const flag = lab.flags?.[key];

                          return (
                            <div key={key} className="flex items-center justify-between text-sm border-b border-gray-100 pb-1">
                              <span className="text-slate-600">
                                {parameter?.name || key}:
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className={`font-medium ${flag ? 'text-yellow-700' : 'text-slate-900'}`}>
                                  {typeof value === 'object' ? value.value : value} {parameter?.unit}
                                </span>
                                {flag && (
                                  <span className="text-xs bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded">
                                    {typeof flag === 'object' ? flag.flag : flag}
                                  </span>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-slate-600">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{format(new Date(lab.testDate), 'MMM dd, yyyy')}</span>
                      </div>
                      {lab.labName && (
                        <span className="text-xs bg-slate-100 px-2 py-1 rounded">
                          {lab.labName}
                        </span>
                      )}
                    </div>

                    {lab.notes && (
                      <div className="text-sm text-slate-600 bg-slate-50 p-2 rounded">
                        <strong>Notes:</strong> {lab.notes}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
