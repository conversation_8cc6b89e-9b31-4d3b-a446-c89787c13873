"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTokenExpired = exports.getTokenExpiration = exports.extractTokenFromHeader = exports.verifyRefreshToken = exports.verifyAccessToken = exports.generateRefreshToken = exports.generateAccessToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
if (!JWT_SECRET || !JWT_REFRESH_SECRET) {
    throw new Error('JWT secrets must be defined in environment variables');
}
const generateAccessToken = (payload) => {
    const options = {
        expiresIn: JWT_EXPIRES_IN,
        issuer: 'psychiatry-app',
        audience: 'psychiatry-app-users',
    };
    return jsonwebtoken_1.default.sign(payload, JWT_SECRET, options);
};
exports.generateAccessToken = generateAccessToken;
const generateRefreshToken = (payload) => {
    const options = {
        expiresIn: JWT_REFRESH_EXPIRES_IN,
        issuer: 'psychiatry-app',
        audience: 'psychiatry-app-users',
    };
    return jsonwebtoken_1.default.sign(payload, JWT_REFRESH_SECRET, options);
};
exports.generateRefreshToken = generateRefreshToken;
const verifyAccessToken = (token) => {
    try {
        console.log('🔍 JWT Debug - Verifying token:', {
            tokenLength: token.length,
            tokenPreview: token.substring(0, 50) + '...',
            secretDefined: !!JWT_SECRET,
            secretLength: JWT_SECRET?.length
        });
        const payload = jsonwebtoken_1.default.verify(token, JWT_SECRET, {
            issuer: 'psychiatry-app',
            audience: 'psychiatry-app-users',
        });
        console.log('✅ JWT Debug - Token verified successfully:', {
            userId: payload.userId,
            username: payload.username,
            role: payload.role,
            exp: payload.exp,
            iat: payload.iat
        });
        return payload;
    }
    catch (error) {
        console.error('❌ JWT Debug - Token verification failed:', {
            errorType: error.constructor.name,
            errorMessage: error.message,
            tokenLength: token.length,
            secretDefined: !!JWT_SECRET,
            secretLength: JWT_SECRET?.length
        });
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            throw new Error('Access token expired');
        }
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            throw new Error('Invalid access token');
        }
        throw new Error('Token verification failed');
    }
};
exports.verifyAccessToken = verifyAccessToken;
const verifyRefreshToken = (token) => {
    try {
        const payload = jsonwebtoken_1.default.verify(token, JWT_REFRESH_SECRET, {
            issuer: 'psychiatry-app',
            audience: 'psychiatry-app-users',
        });
        return payload;
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            throw new Error('Refresh token expired');
        }
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            throw new Error('Invalid refresh token');
        }
        throw new Error('Refresh token verification failed');
    }
};
exports.verifyRefreshToken = verifyRefreshToken;
const extractTokenFromHeader = (authHeader) => {
    if (!authHeader) {
        return null;
    }
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
        return null;
    }
    return parts[1];
};
exports.extractTokenFromHeader = extractTokenFromHeader;
const getTokenExpiration = (token) => {
    try {
        const decoded = jsonwebtoken_1.default.decode(token);
        if (decoded?.exp) {
            return new Date(decoded.exp * 1000);
        }
        return null;
    }
    catch {
        return null;
    }
};
exports.getTokenExpiration = getTokenExpiration;
const isTokenExpired = (token) => {
    const expiration = (0, exports.getTokenExpiration)(token);
    if (!expiration) {
        return true;
    }
    return expiration.getTime() < Date.now();
};
exports.isTokenExpired = isTokenExpired;
//# sourceMappingURL=jwt.js.map