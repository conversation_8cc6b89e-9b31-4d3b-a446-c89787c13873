# Full-Stack Application Bug Fix Instructions

## 🎯 Overview
This application is experiencing cascading failures due to a critical authentication issue. An invalid JWT token is causing backend API errors (500/401), which trigger infinite render loops in the React frontend.

## 🚨 Critical Path
**Complete tasks in this exact order:**

### Task 1: Fix Authentication (CRITICAL)
**Root cause fix - must be completed first**

#### Frontend Fix (Part A)
- **Target**: `frontend/src/api/axiosConfig.ts` or `frontend/src/api/index.ts`
- **Action**: Add request interceptor to send valid JWT tokens
- **Verification**: Check Network tab - Authorization headers should show "Bearer [valid-token]"

#### Backend Fix (Part B)  
- **Target**: `backend/src/middleware/auth.ts`
- **Action**: Add try-catch to return 401 instead of 500 for auth failures
- **Verification**: Invalid tokens should return 401, not 500

### Task 2: Fix React Infinite Loop (HIGH)
- **Target**: `frontend/src/pages/AppointmentsPage.tsx`
- **Issue**: useEffect with error object in dependency array
- **Action**: Remove useEffect, use React Query states directly
- **Verification**: Appointments page loads without console errors

### Task 3: Fix Backend Errors (MEDIUM)
- **Issue A**: TypeError in security middleware
- **Issue B**: 404 for missing /api/users route
- **Action**: Add null checks and create missing route
- **Verification**: No more TypeErrors or 404s in backend logs

## ✅ Final Verification Steps

1. **Restart both servers**
   ```bash
   # Backend
   cd backend && npm run dev
   
   # Frontend (Vite dev server)
   cd frontend && npm run dev
   ```

2. **Test full user flow**
   - [ ] Login successful
   - [ ] Navigate to Appointments page (no crashes)
   - [ ] Navigate to Analytics page (Recharts working)
   - [ ] Check browser console (no errors)
   - [ ] Check backend logs (no 500/401 errors)
   - [ ] Prisma queries executing properly

## 🔍 Success Criteria
- ✅ No authentication errors (401/500)
- ✅ No React infinite render loops
- ✅ All pages load without crashing
- ✅ Clean browser console
- ✅ Clean backend logs

---
**Note**: Each task includes detailed implementation steps in the companion DETAILED_TASKS.md file.