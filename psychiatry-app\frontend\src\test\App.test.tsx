import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import App from '../App'

// Mock the auth context
vi.mock('../features/auth', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  ProtectedRoute: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    user: null,
    isAuthenticated: false,
    login: vi.fn(),
    logout: vi.fn(),
    loading: false,
  }),
}))

// Mock the toast provider
vi.mock('../components/ui/Toast', () => ({
  ToastProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock the error boundary
vi.mock('../components/ErrorBoundary', () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}))

// Mock lazy-loaded components
vi.mock('../features/dashboard/components/DashboardPage', () => ({
  default: () => <div data-testid="dashboard">Dashboard</div>,
}))

vi.mock('../features/auth/components/LoginPage', () => ({
  default: () => <div data-testid="login">Login Page</div>,
}))

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('App Component', () => {
  it('renders without crashing', () => {
    renderWithProviders(<App />)
    // The app should render some content
    expect(document.body).toBeInTheDocument()
  })

  it('renders login page by default when not authenticated', () => {
    renderWithProviders(<App />)
    // Should show login page for unauthenticated users
    expect(screen.getByTestId('login')).toBeInTheDocument()
  })
})
