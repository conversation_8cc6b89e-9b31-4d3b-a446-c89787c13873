# Detailed Implementation Tasks

## 🔐 TASK 1: Fix Authentication

### Part A: Frontend JWT Implementation

**File**: `frontend/src/api/axiosConfig.ts` (or `frontend/src/api/index.ts`)

```typescript
import axios from 'axios';

// Create Axios instance with Vite environment variables
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api',
});

// Request interceptor - adds JWT to all requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export default apiClient;
```

**Also update your login logic** to save the token:
```typescript
// In your login component/store using React Hook Form
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginForm = z.infer<typeof loginSchema>;

const LoginComponent = () => {
  const { register, handleSubmit, formState: { errors } } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema)
  });

  const handleLogin = async (data: LoginForm) => {
    try {
      const response = await apiClient.post('/auth/login', data);
      const { accessToken, refreshToken } = response.data;
      
      // Save tokens to localStorage
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
      
      // Continue with login flow...
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleLogin)} className="space-y-4">
      <input 
        {...register('email')} 
        type="email" 
        className="w-full p-2 border rounded"
        placeholder="Email"
      />
      {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
      
      <input 
        {...register('password')} 
        type="password" 
        className="w-full p-2 border rounded"
        placeholder="Password"
      />
      {errors.password && <p className="text-red-500 text-sm">{errors.password.message}</p>}
      
      <button type="submit" className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">
        Login
      </button>
    </form>
  );
};
```

### Part B: Backend Auth Error Handling

**File**: `backend/src/middleware/auth.ts`

```typescript
// backend/src/middleware/auth.ts

import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken } from '../utils/jwt';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    // Check for missing Authorization header
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        message: 'Authentication failed: No token provided.' 
      });
    }

    const token = authHeader.split(' ')[1];
    
    // Verify token (this will throw if invalid)
    const decoded = verifyAccessToken(token) as any;
    
    // Optional: Verify user exists in database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, email: true, role: true }
    });

    if (!user) {
      return res.status(401).json({ 
        message: 'Authentication failed: User not found.' 
      });
    }
    
    // Attach user to request
    (req as any).user = { ...decoded, dbUser: user };
    
    next();
  } catch (error) {
    // Log the error for debugging
    console.error('Authentication error:', error instanceof Error ? error.message : error);
    
    // Return 401 for any token verification failure
    return res.status(401).json({ 
      message: 'Authentication failed: Invalid or expired token.' 
    });
  }
};
```

---

## ⚛️ TASK 2: Fix React Infinite Loop

### Problem
`AppointmentsPage.tsx` has a useEffect that depends on the `error` object, which is recreated on every render.

**File**: `frontend/src/pages/AppointmentsPage.tsx`

**❌ BEFORE (Remove this pattern):**
```tsx
const { data, error: queryError } = useAppointments();
const [myError, setMyError] = useState(null);

// This causes infinite loop!
useEffect(() => {
  if (queryError) {
    setMyError(queryError);
  }
}, [queryError]); // queryError object changes every render
```

**✅ AFTER (Use this pattern with TypeScript):**
```tsx
import React from 'react';
import { useAppointments } from '../hooks/useAppointments';
import type { Appointment } from '../../shared/types'; // Shared types

interface AppointmentsPageProps {}

const AppointmentsPage: React.FC<AppointmentsPageProps> = () => {
  // Use React Query states directly
  const { data: appointments, isLoading, error } = useAppointments();

  // Loading state with Tailwind styling
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading appointments...</span>
      </div>
    );
  }

  // Error state with Tailwind styling
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-red-50">
        <div className="bg-white p-6 rounded-lg shadow-md max-w-md w-full">
          <div className="flex items-center text-red-600 mb-4">
            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-medium">Error Loading Appointments</h3>
          </div>
          <p className="text-red-700 mb-4">{error.message}</p>
          <button 
            onClick={() => window.location.reload()}
            className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Success state with Tailwind styling
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Appointments</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          New Appointment
        </button>
      </div>
      
      <div className="grid gap-4">
        {appointments && appointments.length > 0 ? (
          appointments.map((appointment: Appointment) => (
            <div key={appointment.id} className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">{appointment.patientName}</h3>
                  <p className="text-gray-600 mt-1">{appointment.type}</p>
                  <div className="flex items-center mt-2 text-sm text-gray-500">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {new Date(appointment.dateTime).toLocaleString()}
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  appointment.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                  appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {appointment.status}
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <svg className="w-24 h-24 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-600 mb-2">No appointments found</h3>
            <p className="text-gray-500">Schedule your first appointment to get started.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppointmentsPage;
```

---

## 🔧 TASK 3: Fix Backend Errors

### Issue A: Security Middleware TypeError

**File**: `backend/src/middleware/security.ts`

**Problem**: `hasOwnProperty` called on null/undefined values

```typescript
function sanitize(obj: any): any {
  // Guard clause - handle non-objects
  if (typeof obj !== 'object' || obj === null || obj === undefined) {
    return obj;
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => sanitize(item));
  }

  // Handle objects safely
  const sanitized = {};
  for (const key in obj) {
    // Use safe hasOwnProperty check
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      sanitized[key] = sanitize(obj[key]);
    }
  }
  
  return sanitized;
}

export { sanitize };
```

### Issue B: Missing Users Route

**Step 1**: Create `backend/src/routes/userRoutes.ts`

```typescript
import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// GET /api/users - Get all users (protected route)
router.get('/', authenticate, async (req: Request, res: Response) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        // Exclude password and sensitive fields
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      data: users,
      count: users.length,
      message: 'Users retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve users',
      error: process.env.NODE_ENV === 'development' ? error : undefined
    });
  }
});

// GET /api/users/:id - Get user by ID (protected route)
router.get('/:id', authenticate, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user,
      message: 'User retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user',
      error: process.env.NODE_ENV === 'development' ? error : undefined
    });
  }
});

export default router;
```

**Step 2**: Update `backend/src/app.ts` or main router file

```typescript
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

// Import your routes
import userRoutes from './routes/userRoutes';
import appointmentRoutes from './routes/appointmentRoutes';
import authRoutes from './routes/authRoutes';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173', // Vite default port
  credentials: true,
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);           // NEW: Add this line
app.use('/api/appointments', appointmentRoutes);

// Health check
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false,
    message: `Route ${req.originalUrl} not found` 
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

export default app;
```

---

## 🧪 Detailed Testing & Verification

### 🔐 Test Task 1: Authentication Fix

#### **Testing Frontend JWT Implementation**

1. **Open Browser DevTools**
   ```
   Press F12 or Ctrl+Shift+I
   Navigate to Network tab
   ```

2. **Clear Network Log & Login**
   ```
   Click "Clear" button in Network tab
   Login to your application
   Watch for API requests
   ```

3. **Verify Authorization Headers**
   ```
   ✅ PASS: Look for requests with headers like:
      Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   
   ❌ FAIL: If you see:
      Authorization: Bearer mock-access-token-12345
      OR no Authorization header at all
   ```

4. **Test Token Storage**
   ```
   Press F12 → Application tab → Local Storage
   ✅ Should see: accessToken with a valid JWT value
   ❌ Should NOT see: mock tokens or empty values
   ```

#### **Testing Backend Auth Error Handling**

1. **Test with Invalid Token**
   ```bash
   # Use curl or Postman to test invalid token
   curl -H "Authorization: Bearer invalid-token" http://localhost:3002/api/appointments
   
   # Expected Response:
   HTTP/1.1 401 Unauthorized
   {
     "message": "Authentication failed: Invalid or expired token."
   }
   ```

2. **Test without Token**
   ```bash
   curl http://localhost:3002/api/appointments
   
   # Expected Response:
   HTTP/1.1 401 Unauthorized
   {
     "message": "Authentication failed: No token provided."
   }
   ```

3. **Check Backend Logs**
   ```
   ✅ PASS: Should see clean 401 responses
   ❌ FAIL: If you see stack traces or 500 Internal Server Error
   ```

---

### ⚛️ Test Task 2: React Infinite Loop Fix

#### **Testing Appointments Page Stability**

1. **Clear Browser Console**
   ```
   Press F12 → Console tab → Click "Clear console" (🚫 icon)
   ```

2. **Navigate to Appointments Page**
   ```
   Click on Appointments in your app navigation
   Watch the console for errors
   ```

3. **Verify No Infinite Loop**
   ```
   ✅ PASS: Page loads normally, console stays clean
   ✅ PASS: Loading spinner appears briefly, then content loads
   ✅ PASS: Error messages (if any) display once and stop
   
   ❌ FAIL: Console shows:
      "Warning: Maximum update depth exceeded"
      OR constant stream of error messages
      OR browser becomes unresponsive
   ```

4. **Test Different States**
   ```
   Test A - Loading State:
   - Refresh page while on Appointments
   - Should see "Loading appointments..." briefly
   
   Test B - Error State:
   - Stop backend server (Ctrl+C)
   - Refresh Appointments page
   - Should see error message once, not continuously
   
   Test C - Success State:
   - Restart backend server
   - Should see appointments list or "No appointments found"
   ```

5. **Performance Check**
   ```
   Press F12 → Performance tab → Click record (⭕)
   Navigate to Appointments page
   Stop recording after 5 seconds
   ✅ PASS: Smooth performance graph, no excessive re-renders
   ❌ FAIL: Jagged performance graph, high CPU usage
   ```

---

### 🔧 Test Task 3: Backend Error Fixes

#### **Testing Security Middleware Fix**

1. **Test with Various Data Types**
   ```bash
   # Test with null data
   curl -X POST http://localhost:3002/api/test-endpoint \
   -H "Content-Type: application/json" \
   -d 'null'
   
   # Test with array data
   curl -X POST http://localhost:3002/api/test-endpoint \
   -H "Content-Type: application/json" \
   -d '[{"key": "value"}]'
   
   # Test with object data
   curl -X POST http://localhost:3002/api/test-endpoint \
   -H "Content-Type: application/json" \
   -d '{"user": {"name": "test"}}'
   ```

2. **Check Backend Console**
   ```
   ✅ PASS: No "TypeError: obj.hasOwnProperty is not a function"
   ✅ PASS: Requests process without middleware crashes
   ❌ FAIL: Still seeing hasOwnProperty TypeErrors
   ```

#### **Testing Users Route Fix**

1. **Test GET /api/users**
   ```bash
   curl http://localhost:3002/api/users
   
   # Expected Response:
   HTTP/1.1 200 OK
   {
     "success": true,
     "data": [],
     "message": "Users retrieved successfully"
   }
   ```

2. **Test GET /api/users/:id**
   ```bash
   curl http://localhost:3002/api/users/123
   
   # Expected Response:
   HTTP/1.1 200 OK
   {
     "success": true,
     "data": {"id": "123", "name": "Placeholder User"},
     "message": "User retrieved successfully"
   }
   ```

3. **Browser Network Tab Verification**
   ```
   Open Network tab in browser
   Navigate to any page that calls /api/users
   ✅ PASS: Should see 200 OK responses
   ❌ FAIL: Still seeing 404 Not Found
   ```

---

### 🎯 Comprehensive Integration Testing

#### **Full Application Flow Test**

1. **Restart Both Servers**
   ```bash
   # Backend Terminal
   cd backend
   npm run dev
   # Wait for "Server running on port 3002"
   
   # Frontend Terminal (new window) - Vite dev server
   cd frontend  
   npm run dev
   # Wait for "Local: http://localhost:5173"
   ```

2. **Complete User Journey**
   ```
   Step 1: Open http://localhost:5173 (Vite default port)
   Step 2: Login with valid credentials
   Step 3: Navigate to Dashboard
   Step 4: Navigate to Appointments page
   Step 5: Navigate to Analytics page (test Recharts)
   Step 6: Navigate to Settings page
   ```

3. **Monitor All Channels**
   ```
   Browser Console (F12):
   ✅ Should be clean with no red errors
   
   Backend Terminal:
   ✅ Should show successful API requests (200, 201 status codes)
   ✅ No stack traces or unhandled errors
   
   Network Tab:
   ✅ All requests should complete successfully
   ✅ Authorization headers present on protected routes
   ```

#### **Stress Testing**

1. **Rapid Navigation Test**
   ```
   Quickly click between different pages 10 times
   ✅ PASS: Smooth transitions, no crashes
   ❌ FAIL: Page freezes or error accumulation
   ```

2. **Refresh Test**
   ```
   On each main page, press Ctrl+R (refresh) 3 times
   ✅ PASS: Consistent loading behavior
   ❌ FAIL: Errors on refresh or inconsistent states
   ```

3. **Multiple Tab Test**
   ```
   Open application in 3 different browser tabs
   Login in all tabs
   Navigate simultaneously
   ✅ PASS: All tabs function independently
   ❌ FAIL: Token conflicts or shared state issues
   ```

---

### 📋 Success Criteria Checklist

#### **Authentication (Task 1)**
- [ ] Valid JWT tokens sent in Authorization headers
- [ ] Backend returns 401 (not 500) for invalid tokens
- [ ] Login saves token to localStorage correctly
- [ ] Protected routes work after login

#### **React Stability (Task 2)**
- [ ] Appointments page loads without infinite loops
- [ ] Console remains clean during navigation
- [ ] Loading/error states display correctly
- [ ] No "Maximum update depth exceeded" warnings

#### **Backend Stability (Task 3)**
- [ ] No hasOwnProperty TypeErrors in logs
- [ ] /api/users returns 200 OK responses
- [ ] Security middleware handles all data types
- [ ] No 404 errors for expected routes

#### **Overall System Health**
- [ ] All pages load successfully
- [ ] Smooth navigation between sections
- [ ] No error accumulation over time
- [ ] Consistent behavior across browser sessions