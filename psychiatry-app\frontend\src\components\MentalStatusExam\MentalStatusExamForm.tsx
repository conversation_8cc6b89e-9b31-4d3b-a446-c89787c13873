import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../ui/card';
import { Button } from '../ui/button';
import { Save, ArrowLeft } from 'lucide-react';

interface MentalStatusExamData {
  // Appearance
  appearance_grooming: string;
  appearance_dress: string;
  appearance_hygiene: string;

  // Behavior
  behavior_eye_contact: string;
  behavior_motor: string;
  behavior_cooperation: string;

  // Speech
  speech_rate: string;
  speech_volume: string;
  speech_tone: string;
  speech_fluency: string;
  speech_articulation: string;

  // Mood and Affect
  mood_reported: string;
  mood_observed: string;
  mood_subjective: string;
  affect_type: string;
  affect_objective: string;
  affect_range: string;
  affect_appropriateness: string;

  // Thought Process
  thought_process: string;
  thought_organization: string;
  thought_flow: string;
  thought_process_organization: string;
  thought_process_flow: string;

  // Thought Content
  thought_content_delusions: string;
  thought_content_obsessions: string;
  thought_content_suicidal: string;
  thought_content_homicidal: string;
  delusions: boolean;
  obsessions: boolean;
  compulsions: boolean;
  phobias: boolean;
  hallucinations: boolean;
  illusions: boolean;
  depersonalization: boolean;
  derealization: boolean;

  // Perceptual Disturbances
  perceptual_hallucinations: string;
  perceptual_illusions: string;

  // Cognitive Function
  cognitive_orientation: string;
  cognitive_attention: string;
  cognitive_memory: string;
  cognitive_abstract_thinking: string;
  orientation_person: boolean;
  orientation_place: boolean;
  orientation_time: boolean;
  orientation_situation: boolean;

  // Risk Assessment
  suicidal_ideation: string;
  suicidal_risk: string;
  homicidal_ideation: string;
  homicidal_risk: string;

  // Insight and Judgment
  insight_level: string;
  judgment_level: string;

  // Additional Notes
  additional_notes: string;
  clinical_notes: string;
  recommendations: string;
  followup_needed: boolean;
}

interface MentalStatusExamFormProps {
  onBack: () => void;
  onSave: (data: MentalStatusExamData) => void;
  patientId: string;
}

export const MentalStatusExamForm: React.FC<MentalStatusExamFormProps> = ({ onBack, onSave, patientId }) => {
  const [formData, setFormData] = useState<MentalStatusExamData>({
    // Appearance
    appearance_grooming: '',
    appearance_dress: '',
    appearance_hygiene: '',

    // Behavior
    behavior_eye_contact: '',
    behavior_motor: '',
    behavior_cooperation: '',

    // Speech
    speech_rate: '',
    speech_volume: '',
    speech_tone: '',
    speech_fluency: '',
    speech_articulation: '',

    // Mood & Affect
    mood_reported: '',
    mood_observed: '',
    mood_subjective: '',
    affect_type: '',
    affect_objective: '',
    affect_range: '',
    affect_appropriateness: '',

    // Thought Process
    thought_process: '',
    thought_organization: '',
    thought_flow: '',
    thought_process_organization: '',
    thought_process_flow: '',

    // Thought Content
    thought_content_delusions: '',
    thought_content_obsessions: '',
    thought_content_suicidal: '',
    thought_content_homicidal: '',
    delusions: false,
    obsessions: false,
    compulsions: false,
    phobias: false,
    hallucinations: false,
    illusions: false,
    depersonalization: false,
    derealization: false,

    // Perceptual Disturbances
    perceptual_hallucinations: '',
    perceptual_illusions: '',

    // Cognitive Function
    cognitive_orientation: '',
    cognitive_attention: '',
    cognitive_memory: '',
    cognitive_abstract_thinking: '',
    orientation_person: false,
    orientation_place: false,
    orientation_time: false,
    orientation_situation: false,

    // Risk Assessment
    suicidal_ideation: '',
    suicidal_risk: '',
    homicidal_ideation: '',
    homicidal_risk: '',

    // Insight and Judgment
    insight_level: '',
    judgment_level: '',

    // Additional Notes
    additional_notes: ''
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    const examData = {
      patientId,
      examDate: new Date().toISOString(),
      examinerId: 'current-user-id', // This should come from auth context
      ...formData
    };
    
    onSave(examData);
  };

  const CheckboxField = ({ label, field, checked }: { label: string; field: string; checked: boolean }) => (
    <label className="flex items-center space-x-2 cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => handleInputChange(field, e.target.checked)}
        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
      />
      <span className="text-sm">{label}</span>
    </label>
  );

  const SelectField = ({ label, field, options, value }: { label: string; field: string; options: string[]; value: string }) => (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <select
        value={value}
        onChange={(e) => handleInputChange(field, e.target.value)}
        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <option value="">Select...</option>
        {options.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    </div>
  );

  const TextAreaField = ({ label, field, value }: { label: string; field: string; value: string }) => (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <textarea
        value={value}
        onChange={(e) => handleInputChange(field, e.target.value)}
        rows={3}
        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Mental Status Examination</h1>
            <p className="text-gray-600">Comprehensive psychiatric assessment</p>
          </div>
        </div>
      </div>

      {/* Appearance */}
      <Card>
        <CardHeader>
          <CardTitle>Appearance</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <SelectField
            label="Grooming"
            field="appearance_grooming"
            options={['well-groomed', 'disheveled', 'inappropriate', 'poor']}
            value={formData.appearance_grooming}
          />
          <SelectField
            label="Dress"
            field="appearance_dress"
            options={['appropriate', 'bizarre', 'seductive', 'inappropriate']}
            value={formData.appearance_dress}
          />
          <SelectField
            label="Hygiene"
            field="appearance_hygiene"
            options={['good', 'poor', 'neglected']}
            value={formData.appearance_hygiene}
          />
        </CardContent>
      </Card>

      {/* Behavior */}
      <Card>
        <CardHeader>
          <CardTitle>Behavior</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <SelectField
            label="Eye Contact"
            field="behavior_eye_contact"
            options={['appropriate', 'poor', 'intense', 'avoidant']}
            value={formData.behavior_eye_contact}
          />
          <SelectField
            label="Motor Activity"
            field="behavior_motor"
            options={['normal', 'agitated', 'retarded', 'restless', 'hyperactive']}
            value={formData.behavior_motor}
          />
          <SelectField
            label="Cooperation"
            field="behavior_cooperation"
            options={['cooperative', 'guarded', 'hostile', 'withdrawn']}
            value={formData.behavior_cooperation}
          />
        </CardContent>
      </Card>

      {/* Speech */}
      <Card>
        <CardHeader>
          <CardTitle>Speech</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <SelectField
            label="Rate"
            field="speech_rate"
            options={['normal', 'pressured', 'slow', 'rapid']}
            value={formData.speech_rate}
          />
          <SelectField
            label="Volume"
            field="speech_volume"
            options={['normal', 'loud', 'soft', 'whispered']}
            value={formData.speech_volume}
          />
          <SelectField
            label="Tone"
            field="speech_tone"
            options={['normal', 'monotone', 'dramatic', 'anxious']}
            value={formData.speech_tone}
          />
          <SelectField
            label="Fluency"
            field="speech_fluency"
            options={['fluent', 'dysfluent', 'stuttering']}
            value={formData.speech_fluency}
          />
        </CardContent>
      </Card>

      {/* Mood & Affect */}
      <Card>
        <CardHeader>
          <CardTitle>Mood & Affect</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextAreaField
              label="Reported Mood"
              field="mood_reported"
              value={formData.mood_reported}
            />
            <TextAreaField
              label="Observed Mood"
              field="mood_observed"
              value={formData.mood_observed}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <SelectField
              label="Affect Type"
              field="affect_type"
              options={['euthymic', 'depressed', 'anxious', 'irritable', 'euphoric']}
              value={formData.affect_type}
            />
            <SelectField
              label="Affect Range"
              field="affect_range"
              options={['full', 'restricted', 'blunted', 'flat']}
              value={formData.affect_range}
            />
            <SelectField
              label="Appropriateness"
              field="affect_appropriateness"
              options={['appropriate', 'inappropriate', 'incongruent']}
              value={formData.affect_appropriateness}
            />
          </div>
        </CardContent>
      </Card>

      {/* Thought Content & Perceptual Disturbances */}
      <Card>
        <CardHeader>
          <CardTitle>Thought Content & Perceptual Disturbances</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <CheckboxField label="Delusions" field="delusions" checked={formData.delusions} />
            <CheckboxField label="Obsessions" field="obsessions" checked={formData.obsessions} />
            <CheckboxField label="Compulsions" field="compulsions" checked={formData.compulsions} />
            <CheckboxField label="Phobias" field="phobias" checked={formData.phobias} />
            <CheckboxField label="Hallucinations" field="hallucinations" checked={formData.hallucinations} />
            <CheckboxField label="Illusions" field="illusions" checked={formData.illusions} />
            <CheckboxField label="Depersonalization" field="depersonalization" checked={formData.depersonalization} />
            <CheckboxField label="Derealization" field="derealization" checked={formData.derealization} />
          </div>
        </CardContent>
      </Card>

      {/* Orientation */}
      <Card>
        <CardHeader>
          <CardTitle>Orientation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <CheckboxField label="Person" field="orientation_person" checked={formData.orientation_person} />
            <CheckboxField label="Place" field="orientation_place" checked={formData.orientation_place} />
            <CheckboxField label="Time" field="orientation_time" checked={formData.orientation_time} />
            <CheckboxField label="Situation" field="orientation_situation" checked={formData.orientation_situation} />
          </div>
        </CardContent>
      </Card>

      {/* Risk Assessment */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-800">Risk Assessment</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectField
            label="Suicidal Ideation"
            field="suicidal_ideation"
            options={['denied', 'passive', 'active', 'with_plan']}
            value={formData.suicidal_ideation}
          />
          <SelectField
            label="Suicidal Risk"
            field="suicidal_risk"
            options={['low', 'moderate', 'high', 'imminent']}
            value={formData.suicidal_risk}
          />
          <SelectField
            label="Homicidal Ideation"
            field="homicidal_ideation"
            options={['denied', 'present', 'with_plan']}
            value={formData.homicidal_ideation}
          />
          <SelectField
            label="Homicidal Risk"
            field="homicidal_risk"
            options={['low', 'moderate', 'high', 'imminent']}
            value={formData.homicidal_risk}
          />
        </CardContent>
      </Card>

      {/* Clinical Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Clinical Notes & Recommendations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <TextAreaField
            label="Clinical Notes"
            field="clinical_notes"
            value={formData.clinical_notes}
          />
          <TextAreaField
            label="Recommendations"
            field="recommendations"
            value={formData.recommendations}
          />
          <CheckboxField 
            label="Follow-up needed" 
            field="followup_needed" 
            checked={formData.followup_needed} 
          />
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
          <Save className="h-4 w-4 mr-2" />
          Save Mental Status Exam
        </Button>
      </div>
    </div>
  );
};
